import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:nextsportz_v2/features/teams/presentation/screens/my_teams_screen.dart';
import 'package:provider/provider.dart' as legacy_provider;
import 'core/providers.dart' as core_providers;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import 'utils/color_notifire.dart';
import 'features/home/<USER>/screens/splash_screen.dart';
import 'core/local/key_value_storage.dart';
import 'core/widgets/responsive_wrapper.dart';
import 'core/config/theme.dart';
import 'core/providers/theme_provider.dart' as theme_providers;
import 'package:flutter_web_plugins/url_strategy.dart';
import 'features/player/presentation/screens/public_player_profile_screen.dart';

// Import all screen widgets for routing
import 'onboarding_screen/second_screen.dart';
import 'features/auth/presentation/screens/login_screen.dart';
import 'features/auth/presentation/screens/forgot_password_screen.dart';
import 'features/auth/presentation/screens/register_screen.dart';
import 'features/auth/presentation/screens/otp_verification_screen.dart';
import 'features/home/<USER>/screens/home_screen.dart';
import 'features/challenges/presentation/screens/challenges_screen.dart';
import 'features/venues/presentation/screens/venues_screen.dart';
import 'features/profile/presentation/screens/performance_analysis_screen.dart';
import 'features/teams/presentation/screens/team_list_screen.dart';
import 'features/teams/presentation/screens/join_team_screen.dart';
import 'features/teams/presentation/screens/team_details_screen.dart';
import 'features/profile/presentation/screens/profile_settings_screen.dart';
import 'features/challenges/presentation/screens/my_matches_screen.dart';
import 'features/profile/presentation/screens/player_rating_details_screen.dart';
import 'features/player/presentation/screens/self_assessment_dialog_screen.dart';

late SharedPreferences sharedPref;

// Define the router configuration for web URL paths
final _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      name: 'splash',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/onboarding',
      name: 'onboarding',
      builder: (context, state) => const SecondScreen(),
    ),
    GoRoute(
      path: '/login',
      name: 'login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/signup',
      name: 'signup',
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(
      path: '/forgot-password',
      name: 'forgot-password',
      builder: (context, state) => const ForgotPasswordScreen(),
    ),
    GoRoute(
      path: '/register',
      name: 'register',
      builder: (context, state) => const RegisterScreen(),
    ),

    GoRoute(
      path: '/otp-verification',
      name: 'otp-verification',
      builder: (context, state) {
        final phoneNumber = state.uri.queryParameters['phoneNumber'] ?? '';
        final role = state.uri.queryParameters['role'] ?? 'PLAYER';
        return OtpVerificationScreen(phoneNumber: phoneNumber, role: role);
      },
    ),
    GoRoute(
      path: '/home',
      name: 'home',
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/challenges',
      name: 'challenges',
      builder: (context, state) => const ChallengesScreen(),
    ),
    GoRoute(
      path: '/venues',
      name: 'venues',
      builder: (context, state) => const VenuesScreen(),
    ),
    GoRoute(
      path: '/performance-analysis',
      name: 'performance-analysis',
      builder: (context, state) => const PerformanceAnalysisScreen(),
    ),
    GoRoute(
      path: '/teams',
      name: 'teams',
      builder: (context, state) => const TeamListScreen(),
      routes: [
        GoRoute(
          path: ':teamId',
          name: 'team-detail',
          builder: (context, state) {
            final teamId = state.pathParameters['teamId']!;
            return TeamDetailsScreen(teamId: teamId, isPublicView: true);
          },
        ),
      ],
    ),
    GoRoute(
      path: '/my-teams',
      name: 'my-teams',
      builder: (context, state) => const MyTeamsScreen(),
      routes: [
        GoRoute(
          path: ':teamId',
          name: 'my-team-detail',
          builder: (context, state) {
            final teamId = state.pathParameters['teamId']!;
            return TeamDetailsScreen(teamId: teamId, isPublicView: false);
          },
        ),
      ],
    ),
    GoRoute(
      path: '/join-team',
      name: 'join-team',
      builder: (context, state) {
        final teamCode = state.uri.queryParameters['teamCode'];
        return JoinTeamScreen(prefilledTeamCode: teamCode);
      },
    ),
    GoRoute(
      path: '/profile-settings',
      name: 'profile-settings',
      builder: (context, state) => const ProfileSettingsScreen(),
    ),
    GoRoute(
      path: '/my-matches',
      name: 'my-matches',
      builder: (context, state) => const MyMatchesScreen(),
    ),
    GoRoute(
      path: '/player-rating/:playerId',
      name: 'player-rating',
      builder: (context, state) {
        final playerId = state.pathParameters['playerId']!;
        return PlayerRatingDetailsScreen(playerId: playerId);
      },
    ),
    GoRoute(
      path: '/players/:playerId',
      name: 'player-profile',
      builder: (context, state) {
        final playerId = state.pathParameters['playerId'];
        return PublicPlayerProfileScreen(playerId: playerId);
      },
    ),
    GoRoute(
      path: '/self-assessment/:playerId',
      name: 'self-assessment',
      builder: (context, state) {
        final playerId = state.pathParameters['playerId']!;
        return SelfAssessmentDialogScreen(playerId: playerId);
      },
    ),
  ],
);

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  sharedPref = await SharedPreferences.getInstance();
  if (kIsWeb) {
    usePathUrlStrategy();
  }
  print('SharedPreferences initialized successfully');
  runApp(
    ProviderScope(
      overrides: [
        core_providers.sharedPrefsProvider.overrideWithValue(sharedPref),
        core_providers.keyValueStorageProvider.overrideWithValue(
          KeyValueStorageServiceImpl(sharedPref),
        ),
      ],
      child: legacy_provider.MultiProvider(
        providers: [
          legacy_provider.ChangeNotifierProvider(
            create: (_) => ColorNotifire(),
          ),
        ],
        child: const NextSportzApp(),
      ),
    ),
  );
}

class NextSportzApp extends ConsumerWidget {
  const NextSportzApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(theme_providers.themeProvider);

    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      builder: (context, child) {
        return ResponsiveWrapper(
          enableGlassEffect: true,
          gradientColors: [
            const Color(0xFF667eea),
            const Color(0xFF764ba2),
            const Color(0xFFf093fb),
          ],
          blurIntensity: 15,
          child: MaterialApp.router(
            title: 'NextSportz',
            debugShowCheckedModeBanner: false,
            theme: NextSportzTheme.lightTheme,
            darkTheme: NextSportzTheme.darkTheme,
            themeMode: themeMode,
            routerConfig: _router,
          ),
        );
      },
    );
  }
}
